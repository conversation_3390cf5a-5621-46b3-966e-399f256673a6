{"$schema": "https://openapi.vercel.sh/vercel.json", "buildCommand": "cd ../.. && pnpm turbo run build --filter=@encreasl/web", "installCommand": "cd ../.. && pnpm install", "framework": "nextjs", "functions": {"apps/web/src/app/api/**/*.ts": {"runtime": "nodejs20.x"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "redirects": [{"source": "/www.encreasl.com/(.*)", "destination": "https://encreasl.com/$1", "permanent": true}]}